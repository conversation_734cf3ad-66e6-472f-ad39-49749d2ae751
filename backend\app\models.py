from sqlalchemy import Column, Integer, String, TIMESTAMP
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class AdminModel(Base):
    __tablename__ = 'admins'

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, comment='管理员的用户名，必须唯一')
    password = Column(String(255), nullable=False, comment='经过哈希处理后的管理员密码')
    created_at = Column(TIMESTAMP, server_default='CURRENT_TIMESTAMP', comment='管理员信息创建的时间')
    updated_at = Column(TIMESTAMP, server_default='CURRENT_TIMESTAMP', onupdate='CURRENT_TIMESTAMP', comment='管理员信息更新的时间')
    status = Column(String(20), default='active', comment='管理员账户的状态，如 active、inactive 等')

    def __repr__(self):
        return f"<AdminModel(id={self.id}, username={self.username}, status={self.status})>"
