import io
import base64
import uuid
import random
import time
import string
import logging
from datetime import datetime, timedelta
from captcha.image import ImageCaptcha
from fastapi import APIRouter, HTTPException, Depends
from app.schemas import CaptchaResponse, CaptchaVerifyRequest

router = APIRouter(prefix="/captcha", tags=["captcha"])

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("captcha_router")

# 使用内存字典存储验证码
captcha_store = {}

# 验证码配置
CAPTCHA_EXPIRY_MINUTES = 10  # 验证码过期时间（分钟）
CAPTCHA_LENGTH = 4  # 验证码长度
CAPTCHA_COMPLEXITY = "mixed"  # 验证码复杂度: "digits", "letters", "mixed"

def clean_expired_captchas():
    """清理过期的验证码"""
    current_time = time.time()
    expired_keys = [k for k, v in captcha_store.items() if current_time > v['expiry']]
    for key in expired_keys:
        del captcha_store[key]
    return len(expired_keys)

def generate_captcha_text(length=CAPTCHA_LENGTH, complexity=CAPTCHA_COMPLEXITY):
    """生成验证码文本"""
    if complexity == "digits":
        characters = string.digits
    elif complexity == "letters":
        characters = string.ascii_uppercase
    else:  # mixed
        characters = string.ascii_uppercase + string.digits
    
    # 确保验证码至少包含一个数字和一个字母（如果是混合类型）
    if complexity == "mixed":
        # 生成至少包含一个数字
        text = random.choice(string.digits)
        # 生成至少包含一个字母
        text += random.choice(string.ascii_uppercase)
        # 生成剩余字符
        text += ''.join(random.choice(characters) for _ in range(length - 2))
        # 打乱顺序
        text_list = list(text)
        random.shuffle(text_list)
        return ''.join(text_list)
    else:
        return ''.join(random.choice(characters) for _ in range(length))

@router.get("/generate", response_model=CaptchaResponse)
async def generate_captcha():
    try:
        # 清理过期验证码
        cleaned = clean_expired_captchas()
        if cleaned > 0:
            logger.info(f"已清理 {cleaned} 个过期验证码")
        
        # 生成随机验证码
        captcha_text = generate_captcha_text()
        logger.info(f"生成验证码: {captcha_text}")
        
        # 生成验证码图片
        image = ImageCaptcha(width=160, height=60)
        image_bytes = image.generate(captcha_text).getvalue()
        
        # 转换为base64
        image_base64 = base64.b64encode(image_bytes).decode()
        
        # 生成唯一ID
        captcha_id = str(uuid.uuid4())
        
        # 计算过期时间
        expiry_time = time.time() + (CAPTCHA_EXPIRY_MINUTES * 60)
        
        # 存储验证码
        captcha_store[captcha_id] = {
            'code': captcha_text,
            'expiry': expiry_time,
            'created_at': time.time()
        }
        
        logger.info(f"验证码已创建，ID: {captcha_id}，将在 {CAPTCHA_EXPIRY_MINUTES} 分钟后过期")
        
        return {
            "captcha_id": captcha_id,
            "image_base64": image_base64,
            "expires_in_seconds": CAPTCHA_EXPIRY_MINUTES * 60
        }
    except Exception as e:
        logger.error(f"生成验证码时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成验证码失败: {str(e)}")

@router.post("/verify/{captcha_id}")
async def verify_captcha(captcha_id: str, request: CaptchaVerifyRequest):
    try:
        # 清理过期验证码
        clean_expired_captchas()
        
        captcha_data = captcha_store.get(captcha_id)
        if not captcha_data:
            logger.warning(f"验证码 ID 不存在或已过期: {captcha_id}")
            raise HTTPException(status_code=400, detail="验证码已过期或不存在")
        
        stored_code = captcha_data['code']
        
        # 检查验证码是否过期
        if time.time() > captcha_data['expiry']:
            del captcha_store[captcha_id]
            logger.warning(f"验证码已过期: {captcha_id}")
            raise HTTPException(status_code=400, detail="验证码已过期")
        
        # 验证后删除存储的验证码
        del captcha_store[captcha_id]
        
        # 不区分大小写验证
        if request.code.upper() != stored_code.upper():
            logger.warning(f"验证码不匹配，ID: {captcha_id}, 输入: {request.code}, 预期: {stored_code}")
            raise HTTPException(status_code=400, detail="验证码错误")
        
        logger.info(f"验证码验证成功，ID: {captcha_id}")
        return {"message": "验证成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证码验证时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证码验证失败: {str(e)}")

@router.get("/stats")
async def get_captcha_stats():
    """获取验证码统计信息（仅供内部使用）"""
    clean_expired_captchas()
    active_count = len(captcha_store)
    return {
        "active_captchas": active_count,
        "config": {
            "expiry_minutes": CAPTCHA_EXPIRY_MINUTES,
            "length": CAPTCHA_LENGTH,
            "complexity": CAPTCHA_COMPLEXITY
        }
    }
