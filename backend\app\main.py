import sys
import os
# 将项目根目录添加到Python路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, Depends
from sqlalchemy.orm import Session
from app.models import AdminModel  # 确保导入 AdminModel
from app.schemas import Admin, AdminCreate, AdminLogin, AdminLoginWithCaptcha  # 导入所有需要的 schemas
from app.database import get_db  # 确保导入获取数据库会话的函数
from passlib.context import CryptContext
from app.routers import user_router, wechat_router, admin_router, captcha_router  # 导入所有路由
from app import connect_to_db
from fastapi.middleware.cors import CORSMiddleware  # 添加CORS中间件

app = FastAPI()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 添加CORS中间件 - 确保在所有路由前添加
app.add_middleware(
    CORSMiddleware,
    # 允许前端源，确保包含确切的前端URL
    allow_origins=["http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 尝试连接到数据库
connect_to_db()

# 包含路由
app.include_router(user_router.router)
app.include_router(wechat_router.router)  
app.include_router(admin_router.router)  
app.include_router(captcha_router.router)  # 添加验证码路由

@app.get("/")
def read_root():
    return {"message": "Welcome to the AI Robot API"}

@app.post("/register")
def register(admin: AdminCreate, db: Session = Depends(get_db)):
    existing_admin = db.query(AdminModel).filter(AdminModel.username == admin.username).first()
    if existing_admin:
        raise HTTPException(status_code=400, detail="Username already exists")
    
    hashed_password = pwd_context.hash(admin.password)  # 哈希密码
    new_admin = AdminModel(username=admin.username, password=hashed_password)
    db.add(new_admin)
    db.commit()
    return {"message": "Admin registered successfully"}

@app.post("/login")
def login(admin: AdminLoginWithCaptcha, db: Session = Depends(get_db)):
    import logging
    logger = logging.getLogger("login")
    
    try:
        logger.info(f"[登录尝试] 用户名: {admin.username}, 验证码ID: {admin.captcha_id}")
        
        # 验证验证码
        captcha_data = captcha_router.captcha_store.get(admin.captcha_id)
        if not captcha_data:
            logger.warning(f"[验证码错误] ID: {admin.captcha_id} 不存在或过期")
            raise HTTPException(status_code=400, detail="验证码已过期")
        
        stored_code = captcha_data.get('code', '')
        logger.info(f"[验证码检查] 用户输入: {admin.captcha_code}, 存储的验证码: {stored_code}")
        
        if admin.captcha_code.lower() != stored_code.lower():
            logger.warning(f"[验证码错误] 用户名: {admin.username}, 输入: {admin.captcha_code}, 实际: {stored_code}")
            raise HTTPException(status_code=400, detail="验证码错误")
        
        # 验证成功后删除验证码
        del captcha_router.captcha_store[admin.captcha_id]
        logger.info(f"[验证码验证成功] 用户名: {admin.username}, 验证码ID: {admin.captcha_id}")
        
        # 验证用户名和密码
        logger.info(f"[查询用户] 用户名: {admin.username}")
        existing_admin = db.query(AdminModel).filter(AdminModel.username == admin.username).first()
        
        if not existing_admin:
            logger.warning(f"[登录失败] 用户不存在: {admin.username}")
            raise HTTPException(status_code=400, detail="用户名或密码错误")
        
        password_verified = pwd_context.verify(admin.password, existing_admin.password)
        if not password_verified:
            logger.warning(f"[登录失败] 密码错误: {admin.username}")
            raise HTTPException(status_code=400, detail="用户名或密码错误")
        
        logger.info(f"[登录成功] 用户名: {admin.username}, ID: {existing_admin.id}")
        # 返回token（在实际应用中，你会生成一个真实的JWT或其他类型的token）
        return {
            "message": "登录成功",
            "token": "test_token_12345",
            "user": {
                "id": existing_admin.id,
                "username": existing_admin.username
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        # 记录完整的错误信息，包括错误类型和堆栈跟踪
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"[登录失败] 意外错误: {str(e)}\n{error_details}")
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")

# 如果直接运行此文件，则启动uvicorn服务器
if __name__ == "__main__":
    import uvicorn
    print("启动FastAPI服务器，请访问 http://127.0.0.1:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
