from pydantic import BaseModel, Field, validator
from typing import Optional

class AdminBase(BaseModel):
    username: str = Field(..., min_length=3)

class AdminCreate(AdminBase):
    password: str = Field(..., min_length=8)
    
    @validator('password')
    def validate_password(cls, password):
        if not any(char.isdigit() for char in password):
            raise ValueError('密码必须包含至少一个数字')
        if not any(char.isupper() for char in password):
            raise ValueError('密码必须包含至少一个大写字母')
        return password

class AdminLogin(AdminBase):
    password: str

class AdminLoginWithCaptcha(AdminLogin):
    captcha_id: str
    captcha_code: str

class Admin(AdminBase):
    id: int
    
    class Config:
        from_attributes = True

class AdminPasswordUpdate(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_password(cls, password):
        if not any(char.isdigit() for char in password):
            raise ValueError('密码必须包含至少一个数字')
        if not any(char.isupper() for char in password):
            raise ValueError('密码必须包含至少一个大写字母')
        return password

class CaptchaResponse(BaseModel):
    captcha_id: str
    image_base64: str
    expires_in_seconds: Optional[int] = None

class CaptchaVerifyRequest(BaseModel):
    code: str = Field(..., min_length=1, description="用户输入的验证码")

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
