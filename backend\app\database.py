from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 从环境变量获取数据库连接信息，如果不存在则使用默认值
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "666666")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_NAME = os.getenv("DB_NAME", "shundao")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

try:
    # 配置数据库引擎，添加连接池设置
    engine = create_engine(
        DATABASE_URL,
        pool_size=5,              # 连接池大小
        max_overflow=10,          # 超过连接池大小外最多创建的连接
        pool_timeout=30,          # 池连接超时时间
        pool_recycle=1800,        # 回收连接的时间(秒)
        pool_pre_ping=True,       # 连接前检查
    )
    logger.info("数据库连接成功配置")
except Exception as e:
    logger.error(f"数据库连接配置错误: {e}")
    raise

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 创建一个依赖项，用于获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        # 记录完整的错误信息，包括错误类型和堆栈跟踪
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"数据库会话错误: {e}\n详细信息: {error_details}")
        db.rollback()
        raise
    finally:
        db.close()
