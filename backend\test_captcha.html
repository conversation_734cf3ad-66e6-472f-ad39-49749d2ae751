<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        img {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input, button {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
        .info {
            background-color: #d9edf7;
            color: #31708f;
            border: 1px solid #bce8f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码测试页面</h1>
        
        <div class="card">
            <h2>1. 生成验证码</h2>
            <button id="generateCaptcha">生成新验证码</button>
            <div class="captcha-container">
                <img id="captchaImage" src="" alt="验证码" style="display: none;">
                <div>
                    <input type="text" id="captchaInput" placeholder="输入验证码">
                    <button id="verifyCaptcha">验证</button>
                </div>
            </div>
            <div>验证码ID: <span id="captchaId"></span></div>
            <div>过期时间: <span id="expiresIn"></span></div>
        </div>
        
        <div class="card">
            <h2>2. 验证码统计</h2>
            <button id="getCaptchaStats">获取统计信息</button>
        </div>
        
        <div id="result" style="display: none;"></div>
    </div>

    <script>
        // API基础URL
        const baseUrl = 'http://localhost:8000';
        
        // 存储当前验证码ID
        let currentCaptchaId = '';
        
        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = typeof message === 'object' ? JSON.stringify(message, null, 2) : message;
            resultDiv.className = type;
            resultDiv.style.display = 'block';
        }
        
        // 生成验证码
        document.getElementById('generateCaptcha').addEventListener('click', async () => {
            try {
                const response = await fetch(`${baseUrl}/captcha/generate`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // 显示验证码图片
                document.getElementById('captchaImage').src = `data:image/png;base64,${data.image_base64}`;
                document.getElementById('captchaImage').style.display = 'block';
                
                // 存储验证码ID
                currentCaptchaId = data.captcha_id;
                document.getElementById('captchaId').textContent = data.captcha_id;
                
                // 显示过期时间
                const expiresInMinutes = Math.floor(data.expires_in_seconds / 60);
                document.getElementById('expiresIn').textContent = `${expiresInMinutes} 分钟`;
                
                // 清空验证码输入框
                document.getElementById('captchaInput').value = '';
                
                // 显示结果
                showResult(data, 'info');
            } catch (error) {
                showResult(`生成验证码失败: ${error.message}`, 'error');
            }
        });
        
        // 验证验证码
        document.getElementById('verifyCaptcha').addEventListener('click', async () => {
            const captchaCode = document.getElementById('captchaInput').value;
            
            if (!currentCaptchaId) {
                showResult('请先生成验证码', 'error');
                return;
            }
            
            if (!captchaCode) {
                showResult('请输入验证码', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${baseUrl}/captcha/verify/${currentCaptchaId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ code: captchaCode }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('验证成功！', 'success');
                    // 清空当前验证码ID
                    currentCaptchaId = '';
                    document.getElementById('captchaId').textContent = '';
                    document.getElementById('captchaImage').style.display = 'none';
                } else {
                    showResult(`验证失败: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult(`验证失败: ${error.message}`, 'error');
            }
        });
        
        // 获取验证码统计信息
        document.getElementById('getCaptchaStats').addEventListener('click', async () => {
            try {
                const response = await fetch(`${baseUrl}/captcha/stats`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                showResult(data, 'info');
            } catch (error) {
                showResult(`获取统计信息失败: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
