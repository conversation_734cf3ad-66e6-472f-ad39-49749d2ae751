from fastapi import FastAPI
from .routers import user_router
import psycopg2
from psycopg2 import sql

def connect_to_db():
    try:
        connection = psycopg2.connect(
            dbname="shundao",
            user="postgres",
            password="666666",
            host="localhost",
            port="5432"
        )
        print("数据库连接成功")
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")

app = FastAPI()

app.include_router(user_router.router)
