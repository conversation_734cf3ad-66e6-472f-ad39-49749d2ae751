from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional

from app.database import get_db
from app import models, schemas

# 配置常量
SECRET_KEY = "YOUR_SECRET_KEY_HERE"  # 在生产环境中应当使用强随机密钥
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

router = APIRouter(prefix="/admin", tags=["admin"])

# 密码哈希工具
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 生成JWT令牌
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 创建管理员
@router.post("/", status_code=status.HTTP_201_CREATED, response_model=schemas.Admin)
def create_admin(admin: schemas.AdminCreate, db: Session = Depends(get_db)):
    # 检查用户名是否已存在
    db_admin = db.query(models.AdminModel).filter(models.AdminModel.username == admin.username).first()
    if db_admin:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    # 创建新管理员，对密码进行哈希处理
    hashed_password = pwd_context.hash(admin.password)
    db_admin = models.AdminModel(username=admin.username, password=hashed_password)
    
    # 添加到数据库
    db.add(db_admin)
    db.commit()
    db.refresh(db_admin)
    
    # 返回创建成功的管理员信息（不包含密码）
    return db_admin

# 管理员登录
@router.post("/login", response_model=schemas.TokenResponse)
def login_admin(username: str, password: str, db: Session = Depends(get_db)):
    # 查找管理员
    db_admin = db.query(models.AdminModel).filter(models.AdminModel.username == username).first()
    if not db_admin:
        raise HTTPException(status_code=401, detail="用户名或密码错误")
    
    # 验证密码
    if not pwd_context.verify(password, db_admin.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": db_admin.username}, expires_delta=access_token_expires
    )
    
    # 返回令牌
    return {"access_token": access_token, "token_type": "bearer"}

# 获取当前管理员信息
@router.get("/me", response_model=schemas.Admin)
def get_current_admin(token: str, db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 解码令牌
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
        
    # 查找管理员
    admin = db.query(models.AdminModel).filter(models.AdminModel.username == username).first()
    if admin is None:
        raise credentials_exception
        
    return admin

# 获取所有管理员
@router.get("/", response_model=list[schemas.Admin])
def get_all_admins(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    admins = db.query(models.AdminModel).offset(skip).limit(limit).all()
    return admins

# 删除管理员
@router.delete("/{admin_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_admin(admin_id: int, db: Session = Depends(get_db)):
    admin = db.query(models.AdminModel).filter(models.AdminModel.id == admin_id).first()
    if not admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
        
    db.delete(admin)
    db.commit()
    return {"detail": "管理员已删除"}

# 修改管理员密码
@router.put("/{admin_id}/password", response_model=schemas.Admin)
def update_admin_password(admin_id: int, password_update: schemas.AdminPasswordUpdate, db: Session = Depends(get_db)):
    admin = db.query(models.AdminModel).filter(models.AdminModel.id == admin_id).first()
    if not admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
        
    # 验证当前密码
    if not pwd_context.verify(password_update.current_password, admin.password):
        raise HTTPException(status_code=400, detail="当前密码不正确")
        
    # 更新密码
    admin.password = pwd_context.hash(password_update.new_password)
    db.commit()
    db.refresh(admin)
    
    return admin
