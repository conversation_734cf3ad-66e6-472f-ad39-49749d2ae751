"""
创建或重置管理员账户密码
"""
from passlib.context import CryptContext
from app.database import SessionLocal
from app.models import AdminModel

def create_or_reset_admin(username="admin", password="admin123"):
    """创建新管理员账户或重置现有账户密码"""
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    db = SessionLocal()
    
    try:
        # 检查用户是否存在
        existing = db.query(AdminModel).filter(AdminModel.username == username).first()
        
        if existing:
            print(f"用户名 {username} 已存在，重置密码")
            existing.password = pwd_context.hash(password)
        else:
            new_admin = AdminModel(username=username, password=pwd_context.hash(password))
            db.add(new_admin)
            print(f"创建新管理员: {username}")
        
        db.commit()
        print(f"管理员账户设置成功! 用户名: {username}, 密码: {password}")
    
    except Exception as e:
        print(f"错误: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_or_reset_admin()
