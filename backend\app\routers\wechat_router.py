from fastapi import APIRouter, HTTPException
import requests

router = APIRouter()

@router.post("/users/wechat_login")
async def wechat_login(code: str):
    # 步骤 1: 使用 code 获取 access_token 和 openid
    response = requests.get(f"https://api.weixin.qq.com/sns/jscode2session?appid=YOUR_APPID&secret=YOUR_SECRET&js_code={{code}}&grant_type=authorization_code")
    data = response.json()
    
    if "errcode" in data:
        raise HTTPException(status_code=400, detail="Invalid code")

    openid = data["openid"]
    
    # 步骤 2: 检查用户是否已注册
    user = users_db.get(openid)
    if user:
        return {"message": "Login successful", "user": user, "token": "mock_token"}
    else:
        # 步骤 3: 注册新用户
        new_user = {"openid": openid, "nickname": "Default Nickname", "avatar": "Default Avatar URL"}
        users_db[openid] = new_user
        return {"message": "Registration successful", "user": new_user, "token": "mock_token"}
