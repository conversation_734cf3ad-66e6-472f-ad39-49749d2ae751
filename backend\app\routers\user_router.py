from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field, EmailStr, validator
from typing import List
import requests

router = APIRouter()

class User(BaseModel):
    id: int
    username: str
    email: str

class UserCreate(BaseModel):
    username: str = Field(..., pattern=r'^[a-zA-Z0-9]+$')  # 仅允许字母和数字
    password: str = Field(..., min_length=8)  # 最少8个字符
    email: EmailStr  # 自动验证电子邮件格式

    @validator('password')
    def validate_password(cls, password):
        if not any(char.isdigit() for char in password):
            raise ValueError('密码必须包含至少一个数字')
        if not any(char.isupper() for char in password):
            raise ValueError('密码必须包含至少一个大写字母')
        if not any(char.islower() for char in password):
            raise ValueError('密码必须包含至少一个小写字母')
        if not any(char in '!@#$%^&*()_+' for char in password):
            raise ValueError('密码必须包含至少一个特殊字符')
        return password

class UserUpdate(BaseModel):
    username: str = None
    email: str = None

# Mock database
users_db = {}
login_attempts = {}

@router.post("/users/register")
async def register_user(user: UserCreate):
    if user.username in users_db:
        raise HTTPException(status_code=400, detail="Username already exists")
    if any(u['email'] == user.email for u in users_db.values()):
        raise HTTPException(status_code=400, detail="Email already exists")
    user_id = len(users_db) + 1
    users_db[user_id] = user.dict()
    return {"id": user_id, "token": "mock_token"}

@router.post("/users/login")
async def login_user(username: str, password: str):
    if username in login_attempts and login_attempts[username] >= 5:
        raise HTTPException(status_code=403, detail="Account locked due to multiple failed login attempts")
    for uid, u in users_db.items():
        if u['username'] == username and u['password'] == password:
            login_attempts[username] = 0
            return {"id": uid, "token": "mock_token"}
    login_attempts[username] = login_attempts.get(username, 0) + 1
    raise HTTPException(status_code=400, detail="Invalid credentials")

@router.get("/users/{user_id}")
async def get_user(user_id: int):
    user = users_db.get(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/users/{user_id}")
async def update_user(user_id: int, user_update: UserUpdate):
    user = users_db.get(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    users_db[user_id].update(user_update.dict(exclude_unset=True))
    return {"status": "success"}

@router.delete("/users/{user_id}")
async def delete_user(user_id: int):
    if user_id in users_db:
        del users_db[user_id]
        return {"status": "success"}
    raise HTTPException(status_code=404, detail="User not found")

@router.post("/users/wechat_login")
async def wechat_login(code: str):
    # 步骤 1: 使用 code 获取 access_token 和 openid
    response = requests.get(f"https://api.weixin.qq.com/sns/jscode2session?appid=YOUR_APPID&secret=YOUR_SECRET&js_code={code}&grant_type=authorization_code")
    data = response.json()
    
    if "errcode" in data:
        raise HTTPException(status_code=400, detail="Invalid code")

    openid = data["openid"]
    
    # 步骤 2: 检查用户是否已注册
    user = users_db.get(openid)
    if user:
        return {"message": "Login successful", "user": user, "token": "mock_token"}
    else:
        # 步骤 3: 注册新用户
        new_user = {"openid": openid, "nickname": "Default Nickname", "avatar": "Default Avatar URL"}
        users_db[openid] = new_user
        return {"message": "Registration successful", "user": new_user, "token": "mock_token"}
